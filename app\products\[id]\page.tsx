import { SellzioProductDetail } from "@/components/sellzio/product-detail"
import { FlashSaleProductDetail } from "@/components/sellzio/flash-sale-product-detail"
import { sampleProducts } from "@/components/data/products"

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  // Check if product is flash sale - hanya berdasarkan flag isFlashSale
  const product = sampleProducts.find(p => p.id === parseInt(params.id))
  const isFlashSale = product?.isFlashSale === true

  // Render appropriate component based on product type
  if (isFlashSale) {
    return <FlashSaleProductDetail productId={params.id} />
  }

  return <SellzioProductDetail productId={params.id} />
}
