/* Sellzio Product Detail Styles */
.sellzio-product-detail {
  min-height: 100vh;
  background-color: #f9fafb;
}

.sellzio-product-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.sellzio-product-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-bottom: 32px;
}

.sellzio-product-image-section {
  position: sticky;
  top: 80px;
  height: fit-content;
}

.sellzio-product-main-image {
  aspect-ratio: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
}

.sellzio-product-info-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.sellzio-product-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  line-height: 1.3;
  margin-bottom: 16px;
}

.sellzio-product-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.sellzio-product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.sellzio-product-sold {
  color: #6b7280;
  font-size: 14px;
}

.sellzio-product-price {
  font-size: 32px;
  font-weight: 700;
  color: #f97316;
  margin-bottom: 24px;
}

.sellzio-product-original-price {
  font-size: 18px;
  color: #9ca3af;
  text-decoration: line-through;
  margin-left: 12px;
}

.sellzio-store-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.sellzio-store-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sellzio-store-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.sellzio-store-location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 14px;
}

.sellzio-shipping-card {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.sellzio-shipping-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #0369a1;
  margin-bottom: 8px;
}

.sellzio-shipping-info {
  color: #0369a1;
  font-size: 14px;
}

.sellzio-quantity-section {
  margin-bottom: 24px;
}

.sellzio-quantity-label {
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
  display: block;
}

.sellzio-quantity-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sellzio-quantity-display {
  min-width: 48px;
  text-align: center;
  font-weight: 600;
  font-size: 16px;
}

.sellzio-action-buttons {
  display: flex;
  gap: 12px;
}

.sellzio-action-button {
  flex: 1;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.sellzio-action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sellzio-add-to-cart {
  background: #f97316;
  color: white;
}

.sellzio-add-to-cart:hover {
  background: #ea580c;
}

.sellzio-wishlist-button,
.sellzio-share-button {
  background: white;
  color: #6b7280;
  border: 1px solid #d1d5db;
  min-width: 48px;
  flex: none;
}

.sellzio-wishlist-button:hover,
.sellzio-share-button:hover {
  background: #f9fafb;
  color: #374151;
}

.sellzio-wishlist-button.active {
  color: #ef4444;
  border-color: #ef4444;
}

.sellzio-description-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sellzio-description-title {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 16px;
}

.sellzio-description-content {
  color: #374151;
  line-height: 1.6;
}

.sellzio-back-button {
  background: transparent;
  border: none;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}

.sellzio-back-button:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .sellzio-product-detail-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .sellzio-product-image-section {
    position: static;
  }
  
  .sellzio-product-title {
    font-size: 20px;
  }
  
  .sellzio-product-price {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .sellzio-product-detail-container {
    padding: 12px;
  }

  .sellzio-product-info-section {
    padding: 16px;
  }

  .sellzio-product-title {
    font-size: 18px;
  }

  .sellzio-product-price {
    font-size: 24px;
  }

  .sellzio-action-buttons {
    flex-direction: row;
    gap: 8px;
  }

  .sellzio-wishlist-button,
  .sellzio-share-button {
    min-width: 44px;
    flex: none;
  }

  .sellzio-add-to-cart {
    flex: 1;
  }

  .sellzio-quantity-controls {
    justify-content: center;
  }
}
