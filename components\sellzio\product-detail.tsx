"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { ChevronLeft, Star, MapPin, Truck, Shield, Heart, Share2, ShoppingCart, Minus, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "@/components/ui/use-toast"
import { SellzioHeader } from "@/components/themes/sellzio/sellzio-header"
import { useCart } from "@/hooks/use-cart"
import { sampleProducts, type Product } from "@/components/data/products"
import "./product-detail.css"

interface SellzioProductDetailProps {
  productId: string
}

export function SellzioProductDetail({ productId }: SellzioProductDetailProps) {
  const router = useRouter()
  const { addToCart } = useCart()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isWishlisted, setIsWishlisted] = useState(false)

  useEffect(() => {
    // Simulasi fetch data produk dari sampleProducts
    const foundProduct = sampleProducts.find(p => p.id === parseInt(productId))
    if (foundProduct) {
      setProduct(foundProduct)
    }
    setLoading(false)
  }, [productId])

  const handleAddToCart = () => {
    if (!product) return
    
    addToCart({
      id: product.id.toString(),
      name: product.name,
      price: parseFloat(product.price.replace(/[^\d]/g, '')),
      image: product.image,
      quantity: quantity
    })
    
    toast({
      title: "Berhasil ditambahkan",
      description: `${product.name} telah ditambahkan ke keranjang`,
    })
  }

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast({
      title: isWishlisted ? "Dihapus dari wishlist" : "Ditambahkan ke wishlist",
      description: `${product?.name} ${isWishlisted ? "dihapus dari" : "ditambahkan ke"} wishlist`,
    })
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.name,
        text: `Lihat produk ${product?.name} di Sellzio`,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link disalin",
        description: "Link produk telah disalin ke clipboard",
      })
    }
  }

  const increaseQuantity = () => setQuantity(prev => prev + 1)
  const decreaseQuantity = () => setQuantity(prev => Math.max(1, prev - 1))

  if (loading) {
    return (
      <div className="sellzio-product-detail">
        <SellzioHeader />
        <div className="pt-16">
          <div className="sellzio-product-detail-container">
            <Skeleton className="h-8 w-32 mb-4" />
            <div className="sellzio-product-detail-grid">
              <Skeleton className="aspect-square w-full" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-6 w-1/2" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="sellzio-product-detail">
        <SellzioHeader />
        <div className="pt-16">
          <div className="sellzio-product-detail-container text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Produk tidak ditemukan</h1>
            <p className="text-gray-600 mb-6">Produk yang Anda cari tidak ditemukan atau telah dihapus.</p>
            <Button onClick={() => router.push('/sellzio')}>
              Kembali ke Beranda
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const rating = parseFloat(product.rating)
  const sold = parseInt(product.sold.replace(/[^\d]/g, ''))

  return (
    <div className="sellzio-product-detail">
      <SellzioHeader />

      {/* Content */}
      <div className="pt-16">
        <div className="sellzio-product-detail-container">
          {/* Back Button */}
          <button
            className="sellzio-back-button"
            onClick={() => router.back()}
          >
            <ChevronLeft className="h-5 w-5" />
            Kembali
          </button>

          {/* Product Detail */}
          <div className="sellzio-product-detail-grid">
            {/* Product Images */}
            <div className="sellzio-product-image-section">
              <div className="sellzio-product-main-image">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-contain p-4"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />

                {/* Badges */}
                {product.isMall && (
                  <Badge className="absolute top-4 left-4 bg-orange-500 text-white">
                    Mall
                  </Badge>
                )}

                {product.discount && (
                  <Badge className="absolute top-4 right-4 bg-red-500 text-white">
                    -{product.discount}
                  </Badge>
                )}
              </div>
            </div>

            {/* Product Info */}
            <div className="sellzio-product-info-section">
              <div>
                <h1 className="sellzio-product-title">{product.name}</h1>

                {/* Rating & Sold */}
                <div className="sellzio-product-meta">
                  <div className="sellzio-product-rating">
                    <Star className="h-4 w-4 fill-red-500 text-red-500" />
                    <span className="text-sm font-medium">{rating}</span>
                  </div>
                  <div className="sellzio-product-sold">
                    {sold.toLocaleString()} terjual
                  </div>
                  {product.cod && (
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      COD
                    </Badge>
                  )}
                </div>

                {/* Price */}
                <div className="sellzio-product-price">
                  {product.price}
                  {product.originalPrice && (
                    <span className="sellzio-product-original-price">
                      {product.originalPrice}
                    </span>
                  )}
                </div>
              </div>

              {/* Store Info */}
              <div className="sellzio-store-card">
                <div className="sellzio-store-info">
                  <div>
                    <h3 className="sellzio-store-name">{product.storeName}</h3>
                    <div className="sellzio-store-location">
                      <MapPin className="h-4 w-4" />
                      {product.address.city}, {product.address.province}
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    Kunjungi Toko
                  </Button>
                </div>
              </div>

              {/* Shipping Info */}
              <div className="sellzio-shipping-card">
                <div className="sellzio-shipping-title">
                  <Truck className="h-4 w-4" />
                  <span>Pengiriman</span>
                </div>
                <p className="sellzio-shipping-info">{product.shipping}</p>
              </div>

              {/* Quantity & Actions */}
              <div className="sellzio-quantity-section">
                <label className="sellzio-quantity-label">Jumlah</label>
                <div className="sellzio-quantity-controls">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={decreaseQuantity}
                    disabled={quantity <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="sellzio-quantity-display">{quantity}</span>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={increaseQuantity}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="sellzio-action-buttons">
                <button
                  className={`sellzio-action-button sellzio-wishlist-button ${isWishlisted ? "active" : ""}`}
                  onClick={handleWishlist}
                >
                  <Heart className={`h-4 w-4 ${isWishlisted ? "fill-current" : ""}`} />
                </button>

                <button
                  className="sellzio-action-button sellzio-share-button"
                  onClick={handleShare}
                >
                  <Share2 className="h-4 w-4" />
                </button>

                <button
                  className="sellzio-action-button sellzio-add-to-cart"
                  onClick={handleAddToCart}
                >
                  <ShoppingCart className="h-4 w-4" />
                  Tambah ke Keranjang
                </button>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <div className="sellzio-description-card">
            <h2 className="sellzio-description-title">Deskripsi Produk</h2>
            <div className="sellzio-description-content">
              <p>
                {product.name} adalah produk berkualitas tinggi yang tersedia di {product.storeName}.
                Produk ini telah terjual sebanyak {sold.toLocaleString()} unit dengan rating {rating} bintang
                dari para pembeli. Tersedia pengiriman {product.shipping.toLowerCase()} ke seluruh Indonesia.
                {product.cod && " Mendukung pembayaran COD (Cash on Delivery) untuk kemudahan berbelanja Anda."}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
